#!/bin/bash

# Display what's happening
echo "Starting Interview Coder application..."

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Kill any existing processes that might interfere
pkill -f "node.*vite" 2>/dev/null || true
pkill -f "electron" 2>/dev/null || true
sleep 1

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Make sure dependencies are installed
if [ ! -d "node_modules" ] || [ ! -d "node_modules/electron" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Start the development server in a separate terminal
echo "Starting development server..."
osascript -e 'tell application "Terminal" to do script "cd \"'$(pwd)'\" && npm run dev"'

# Wait for the server to be ready
echo "Waiting for development server to be ready..."
timeout=30
counter=0
while ! curl -s http://localhost:5173 > /dev/null 2>&1; do
    sleep 1
    counter=$((counter + 1))
    echo "Waiting for server... $counter/$timeout"
    if [ $counter -ge $timeout ]; then
        echo "Timeout waiting for development server"
        exit 1
    fi
done

# Start Electron in a separate terminal
echo "Starting Electron application..."
osascript -e 'tell application "Terminal" to do script "cd \"'$(pwd)'\" && npx electron ."'

echo "Application started successfully!"